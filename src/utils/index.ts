import type { ClassValue } from 'clsx';

import { clsx } from 'clsx';
import { isArray } from 'lodash';
import { twMerge } from 'tailwind-merge';

import type { RecordAny } from '@/types/entity';

/**
 * 合并多个类名值为一个字符串
 *
 * @param inputs - 类名值数组，可以是字符串、对象或数组等形式的类名
 * @returns 合并后的类名字符串，相同类名会进行去重合并
 */
export function mergeClassname(...inputs: ClassValue[]) {
    // 使用clsx处理类名值数组，然后通过twMerge进行合并和优化
    return twMerge(clsx(inputs));
}

/**
 * join url parts
 * @example
 * urlJoin('/admin/', '/api/', '/user/') // '/admin/api/user'
 * urlJoin('/admin', 'api', 'user/')     // '/admin/api/user'
 * urlJoin('/admin/', '', '/user/')      // '/admin/user'
 */
export const urlJoin = (...parts: string[]) => {
    const result = parts
        .map((part) => {
            return part.replace(/^\/+|\/+$/g, ''); // 去除两边/
        })
        .filter(Boolean);
    return `/${result.join('/')}`;
};

export function flattenRoutes(arr: RecordAny<unknown>[]) {
    arr.reduce((pre: RecordAny<unknown>[], item: RecordAny<unknown>) => {
        if (isArray(item.routes)) {
            pre.push(item);
        }
        return pre.concat(isArray(item.routes) ? flattenRoutes(item.routes) : item);
    }, []);
}
